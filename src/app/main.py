from fastapi import FastAP<PERSON>
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from langchain_core.messages import HumanMessage, AIMessage, BaseMessage
from src.app.graph.graph import graph

app = FastAPI(title="Target Search Bot")

class ChatRequest(BaseModel):
    user_input: str
    messages: Optional[List[Dict[str, Any]]] = []

class ChatResponse(BaseModel):
    message: str
    messages: List[Dict[str, Any]]

def message_to_dict(msg: BaseMessage) -> Dict[str, Any]:
    """Convert LangChain message to dict for API response"""
    if isinstance(msg, HumanMessage):
        return {"role": "user", "content": msg.content}
    elif isinstance(msg, AIMessage):
        return {"role": "assistant", "content": msg.content}
    else:
        return {"role": "unknown", "content": str(msg.content)}

def dict_to_message(msg_dict: Dict[str, Any]) -> BaseMessage:
    """Convert dict to LangChain message"""
    if msg_dict["role"] == "user":
        return HumanMessage(content=msg_dict["content"])
    elif msg_dict["role"] == "assistant":
        return AIMessage(content=msg_dict["content"])
    else:
        return HumanMessage(content=msg_dict["content"])  # Default to user message

@app.post("/chat", response_model=ChatResponse)
def chat(req: ChatRequest):
    # Convert existing messages to LangChain format
    existing_messages = [dict_to_message(msg) for msg in (req.messages or [])]

    # Add new user message
    user_message = HumanMessage(content=req.user_input)
    all_messages = existing_messages + [user_message]

    # Process through graph
    out = graph.invoke({"messages": all_messages})

    # Convert messages back to dict format for response
    final_messages = out.get("messages", [])
    messages_as_dicts = [message_to_dict(msg) for msg in final_messages]

    # Get latest AI response
    latest_response = ""
    if final_messages:
        last_msg = final_messages[-1]
        if isinstance(last_msg, AIMessage):
            latest_response = last_msg.content

    return ChatResponse(message=latest_response, messages=messages_as_dicts)

from langgraph.graph import StateGraph
from src.app.graph.state import BotState
from src.app.graph.nodes import extract_entities, call_api, format_response

def build_graph():
    g = StateGraph(BotState)
    g.add_node("extract_entities", extract_entities)
    g.add_node("call_api", call_api)
    g.add_node("format_response", format_response)

    g.set_entry_point("extract_entities")
    g.add_edge("extract_entities", "call_api")
    g.add_edge("call_api", "format_response")
    g.set_finish_point("format_response")

    return g.compile()

graph = build_graph()

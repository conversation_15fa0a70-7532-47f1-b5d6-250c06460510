from typing import Dict, Any, List, Annotated, TypedDict
from langgraph.graph.message import add_messages
from langchain_core.messages import BaseMessage

class BotState(TypedDict):
    # Built-in LangGraph messages handling with reducer
    messages: Annotated[List[BaseMessage], add_messages]
    # Processing state (cleared between conversations)
    entities: Dict[str, str]         # {"sector": "pharmacy", "country": "uk", "continent": "Europe"}
    ids: Dict[str, int]              # {"sector_id": 57, "country_id": 184, "continent_id": 4}
    results: List[Dict[str, Any]]

from typing import Dict
from langchain_core.messages import HumanMessage, AIMessage
from ..services.api_client import DataPlatformClient
from ..services.llm_service import LLMEntityExtractor

# Initialize LLM entity extractor
_llm_extractor = LLMEntityExtractor()

# Extract entities from the latest user message
def extract_entities(state: Dict):
    messages = state.get("messages", [])
    user_input = ""

    # Get the last user message
    if messages:
        for msg in reversed(messages):
            if isinstance(msg, HumanMessage):
                user_input = msg.content
                break

    # Use LLM to extract entities
    extracted = _llm_extractor.extract_entities(user_input)

    # Separate entity names and IDs
    entities = {}
    ids = {}

    for k, v in extracted.items():
        if v is not None:
            if k.endswith("_id"):
                ids[k] = v
            else:
                entities[k] = v

    state["entities"] = entities
    state["ids"] = ids
    return state


# Call API with resolved IDs
_client = DataPlatformClient()

def call_api(state: Dict):
    ids = state.get("ids", {})

    results = _client.get_targets_no_buyer(
        continent_id=ids.get("continent_id"),
        country_id=ids.get("country_id"),
        sector_id=ids.get("sector_id"),
        region=None,
        anonymized_companies=False,
    )
    state["results"] = results
    return state

# Format output and add AI message to conversation
# Format output and add AI message to conversation
def format_response(state: Dict):
    results = state.get("results") or []
    if not results:
        response = "I couldn’t find results for that query."
    else:
        # keep the table readable
        top = results[:50]

        # preferred columns if present; fall back to whatever exists
        preferred_cols = ["company_name", "locations_count", "country", "score"]
        cols = [c for c in preferred_cols if any(c in r for r in top)]
        if not cols:
            # union of keys across rows, capped to first 6 to avoid super-wide tables
            cols = sorted({k for r in top for k in r.keys()})[:6]

        def cell(val):
            s = "" if val is None else str(val)
            # escape characters that break Markdown tables
            return s.replace("|", "\\|").replace("\n", " ").strip()

        header = "| " + " | ".join(col.replace("_", " ").title() for col in cols) + " |"
        sep = "|" + "|".join([" --- "]*len(cols)) + "|"
        rows = ["| " + " | ".join(cell(r.get(c)) for c in cols) + " |" for r in top]

        response = "\n".join(
            [f"Here are the top {len(top)} matches (table):", "", header, sep, *rows]
        )

    return {"messages": [AIMessage(content=response)]}

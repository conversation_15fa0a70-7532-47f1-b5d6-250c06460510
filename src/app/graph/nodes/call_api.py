from typing import Dict
from ...services.api_client import DataPlatformClient

# Initialize API client
_client = DataPlatformClient()

def call_api(state: Dict):
    ids = state.get("ids", {})

    results = _client.get_targets_no_buyer(
        continent_id=ids.get("continent_id"),
        country_id=ids.get("country_id"),
        sector_id=ids.get("sector_id"),
        region=None,
        anonymized_companies=False,
    )
    state["results"] = results
    return state

from typing import Dict
from langchain_core.messages import AIMessage

# Format output and add AI message to conversation
def format_response(state: Dict):
    results = state.get("results") or []
    if not results:
        response = "I couldn't find results for that query."
    else:
        # keep the table readable
        top = results[:50]

        # preferred columns if present; fall back to whatever exists
        preferred_cols = ["company_name", "locations_count", "country", "score"]
        cols = [c for c in preferred_cols if any(c in r for r in top)]
        if not cols:
            # union of keys across rows, capped to first 6 to avoid super-wide tables
            cols = sorted({k for r in top for k in r.keys()})[:6]

        def cell(val):
            s = "" if val is None else str(val)
            # escape characters that break Markdown tables
            return s.replace("|", "\\|").replace("\n", " ").strip()

        header = "| " + " | ".join(col.replace("_", " ").title() for col in cols) + " |"
        sep = "|" + "|".join([" --- "]*len(cols)) + "|"
        rows = ["| " + " | ".join(cell(r.get(c)) for c in cols) + " |" for r in top]

        response = "\n".join(
            [f"Here are the top {len(top)} matches (table):", "", header, sep, *rows]
        )

    return {"messages": [AIMessage(content=response)]}

from typing import Dict
from langchain_core.messages import HumanMessage
from ...services.llm_service import LLMEntityExtractor

# Initialize LLM entity extractor
_llm_extractor = LLMEntityExtractor()

# Extract entities from the latest user message
def extract_entities(state: Dict):
    messages = state.get("messages", [])
    user_input = ""

    # Get the last user message
    if messages:
        for msg in reversed(messages):
            if isinstance(msg, HumanMessage):
                user_input = msg.content
                break

    # Use LLM to extract entities
    extracted = _llm_extractor.extract_entities(user_input)

    # Separate entity names and IDs
    entities = {}
    ids = {}

    for k, v in extracted.items():
        if v is not None:
            if k.endswith("_id"):
                ids[k] = v
            else:
                entities[k] = v

    state["entities"] = entities
    state["ids"] = ids
    return state

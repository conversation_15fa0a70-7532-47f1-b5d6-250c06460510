import httpx
from typing import Any, Dict, List
from src.app.config import API_BASE_URL, API_TOKEN

HEADERS = {
    "accept": "application/json",
    "Authorization": f"Bearer {API_TOKEN}",
}

class DataPlatformClient:
    def __init__(self, base_url: str = API_BASE_URL, headers: Dict[str, str] = HEADERS):
        self.base_url = base_url.rstrip("/")
        self.headers = headers

    def get_targets_no_buyer(
        self,
        *,
        continent_id: int | None = None,
        country_id: int | None = None,
        sector_id: int | None = None,
        region: str | None = None,
        anonymized_companies: bool = False,
        orient: str = "records",
    ) -> List[Dict[str, Any]]:
        params = {
            "continent_id": continent_id,
            "country_id": country_id,
            "sector_id": sector_id,
            "region": region,
            "anonymized_companies": str(anonymized_companies).lower(),
            "orient": orient,
        }
        url = f"{self.base_url}/target_search/get_targets_no_buyer"
        with httpx.Client(timeout=30.0) as client:
            r = client.get(url, headers=self.headers, params=params)
            r.raise_for_status()
            return r.json()  # expecting list[record]

    def get_reach(self, cib_ids: list[str], metric_type: str = "POPULATION", metric_geography: str = "LOCATION"):
        url = f"{self.base_url}/company_extended_metrics/get_metric_reach"
        payload = {
            "metric_type": metric_type,
            "metric_geography": metric_geography,  # LOCATION or other
            "cib_ids": cib_ids,
            "orient": "records"
        }
        r = httpx.post(url, headers={**self.headers, "Content-Type": "application/json"}, json=payload, timeout=30.0)
        r.raise_for_status()
        return r.json()


import os
import requests
from functools import lru_cache
from typing import Dict, List, Any

API_BASE = os.getenv("API_BASE", "https://dataplatform.synergy-impact.de")
API_TOKEN = os.getenv("API_TOKEN", "cloud-ib123")

HEADERS = {
    "accept": "application/json",
    "Authorization": f"Bearer {API_TOKEN}"
}


class LookupCache:
    """
    Singleton for caching static lookup tables:
    - continents
    - countries
    - sectors
    """

    @staticmethod
    @lru_cache(maxsize=1)
    def get_continents() -> List[Dict[str, Any]]:
        url = f"{API_BASE}/common/get_all_continents?orient=records"
        resp = requests.get(url, headers=HEADERS)
        resp.raise_for_status()
        return resp.json()

    @staticmethod
    @lru_cache(maxsize=1)
    def get_countries() -> List[Dict[str, Any]]:
        url = f"{API_BASE}/common/get_all_countries?orient=records"
        resp = requests.get(url, headers=HEADERS)
        resp.raise_for_status()
        return resp.json()

    @staticmethod
    @lru_cache(maxsize=1)
    def get_sectors() -> List[Dict[str, Any]]:
        url = f"{API_BASE}/common/get_all_sectors?orient=records"
        resp = requests.get(url, headers=HEADERS)
        resp.raise_for_status()
        return resp.json()


# Example helper search functions (to be bound as a "tool")
def find_country_id(name: str) -> int | None:
    countries = LookupCache.get_countries()
    for c in countries:
        if c["country_name"].lower() == name.lower():
            return c["country_id"]
    return None


def find_continent_id(name: str) -> int | None:
    continents = LookupCache.get_continents()
    for c in continents:
        if c["continent_name"].lower() == name.lower():
            return c["continent_id"]
    return None


def find_sector_id(name: str) -> int | None:
    sectors = LookupCache.get_sectors()
    for s in sectors:
        if s["sector_name"].lower() == name.lower():
            return s["sector_id"]
    return None

